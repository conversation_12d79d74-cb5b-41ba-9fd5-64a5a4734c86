/**
 * SEO utilities for meta descriptions and other SEO-related functionality
 */

// SEO constants following best practices
export const SEO_CONSTANTS = {
  META_DESCRIPTION_MAX_LENGTH: 160,
  META_DESCRIPTION_MIN_LENGTH: 120,
  SITE_NAME: '<PERSON>',
  SITE_URL: 'https://samuelduval.me',
  DEFAULT_AUTHOR: '<PERSON>',
  DEFAULT_LOCALE: 'fr-FR',
} as const;

// Default meta descriptions for different page types
export const DEFAULT_DESCRIPTIONS = {
  homepage: '<PERSON>, développeur 3D à Nantes. Tech Lead chez Wonder Partner\'s, spécialisé en développement Unity, réalité augmentée et solutions innovantes.',
  blog: 'Articles et réflexions de <PERSON> sur le développement, Unity, les technologies 3D, l\'ingénierie logicielle et l\'innovation.',
  blogPost: 'Article de <PERSON> sur le développement et les technologies.',
  notFound: 'Page non trouvée sur le site de <PERSON>, développeur 3D à Nantes.',
} as const;

/**
 * Truncates text to fit within SEO meta description limits
 * Ensures the text ends at a word boundary and adds ellipsis if truncated
 */
export function truncateDescription(text: string, maxLength: number = SEO_CONSTANTS.META_DESCRIPTION_MAX_LENGTH): string {
  if (!text) return '';
  
  // Remove extra whitespace and normalize
  const cleanText = text.replace(/\s+/g, ' ').trim();
  
  if (cleanText.length <= maxLength) {
    return cleanText;
  }
  
  // Find the last space before the max length to avoid cutting words
  const truncated = cleanText.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  // If we can't find a space, just cut at max length
  if (lastSpaceIndex === -1) {
    return truncated.substring(0, maxLength - 3) + '...';
  }
  
  return truncated.substring(0, lastSpaceIndex) + '...';
}

/**
 * Extracts a description from markdown content
 * Takes the first paragraph and cleans it of markdown syntax
 */
export function extractDescriptionFromContent(content: string): string {
  if (!content) return '';
  
  // Remove frontmatter if present
  const contentWithoutFrontmatter = content.replace(/^---[\s\S]*?---\n/, '');
  
  // Split into paragraphs and find the first substantial one
  const paragraphs = contentWithoutFrontmatter
    .split('\n\n')
    .map(p => p.trim())
    .filter(p => p.length > 0 && !p.startsWith('#') && !p.startsWith('```'));
  
  if (paragraphs.length === 0) return '';
  
  let firstParagraph = paragraphs[0];
  
  // Clean markdown syntax
  firstParagraph = firstParagraph
    // Remove markdown links but keep the text
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Remove bold/italic markers
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/__([^_]+)__/g, '$1')
    .replace(/_([^_]+)_/g, '$1')
    // Remove inline code
    .replace(/`([^`]+)`/g, '$1')
    // Remove HTML tags
    .replace(/<[^>]+>/g, '')
    // Clean up extra whitespace
    .replace(/\s+/g, ' ')
    .trim();
  
  return firstParagraph;
}

/**
 * Generates a meta description for a blog post
 * Uses frontmatter description if available, otherwise extracts from content
 */
export function generateBlogPostDescription(frontmatter: any, content?: string): string {
  // Use explicit description from frontmatter if available
  if (frontmatter?.description) {
    return truncateDescription(frontmatter.description);
  }
  
  // Try to extract from content
  if (content) {
    const extracted = extractDescriptionFromContent(content);
    if (extracted) {
      return truncateDescription(extracted);
    }
  }
  
  // Fallback to default with title if available
  if (frontmatter?.title) {
    return truncateDescription(`${frontmatter.title} - ${DEFAULT_DESCRIPTIONS.blogPost}`);
  }
  
  return DEFAULT_DESCRIPTIONS.blogPost;
}

/**
 * Generates structured data for SEO
 */
export function generateStructuredData(type: 'website' | 'article', data: {
  title: string;
  description: string;
  url: string;
  author?: string;
  datePublished?: string;
  dateModified?: string;
}) {
  const baseStructure = {
    '@context': 'https://schema.org',
    '@type': type === 'website' ? 'WebSite' : 'Article',
    name: data.title,
    description: data.description,
    url: data.url,
  };

  if (type === 'article') {
    return {
      ...baseStructure,
      author: {
        '@type': 'Person',
        name: data.author || DEFAULT_DESCRIPTIONS.homepage,
      },
      datePublished: data.datePublished,
      dateModified: data.dateModified || data.datePublished,
    };
  }

  return baseStructure;
}

/**
 * Validates a meta description according to SEO best practices
 */
export function validateMetaDescription(description: string): {
  isValid: boolean;
  warnings: string[];
  length: number;
} {
  const warnings: string[] = [];
  const length = description.length;
  
  if (length === 0) {
    warnings.push('Meta description is empty');
  } else if (length < SEO_CONSTANTS.META_DESCRIPTION_MIN_LENGTH) {
    warnings.push(`Meta description is too short (${length} chars). Recommended: ${SEO_CONSTANTS.META_DESCRIPTION_MIN_LENGTH}-${SEO_CONSTANTS.META_DESCRIPTION_MAX_LENGTH} chars`);
  } else if (length > SEO_CONSTANTS.META_DESCRIPTION_MAX_LENGTH) {
    warnings.push(`Meta description is too long (${length} chars). Recommended: ${SEO_CONSTANTS.META_DESCRIPTION_MIN_LENGTH}-${SEO_CONSTANTS.META_DESCRIPTION_MAX_LENGTH} chars`);
  }
  
  // Check for duplicate words (potential keyword stuffing)
  const words = description.toLowerCase().split(/\s+/);
  const wordCount = words.reduce((acc, word) => {
    acc[word] = (acc[word] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const duplicateWords = Object.entries(wordCount)
    .filter(([word, count]) => count > 2 && word.length > 3)
    .map(([word]) => word);
  
  if (duplicateWords.length > 0) {
    warnings.push(`Potential keyword stuffing detected: ${duplicateWords.join(', ')}`);
  }
  
  return {
    isValid: warnings.length === 0,
    warnings,
    length,
  };
}
