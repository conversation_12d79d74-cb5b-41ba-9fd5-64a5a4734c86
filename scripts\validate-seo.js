#!/usr/bin/env node

/**
 * SEO Validation Script
 * Validates meta descriptions and other SEO elements in the built site
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');
const SEO_CONSTANTS = {
  META_DESCRIPTION_MAX_LENGTH: 160,
  META_DESCRIPTION_MIN_LENGTH: 120,
};

function extractMetaDescription(html) {
  const match = html.match(/<meta name="description" content="([^"]*)"[^>]*>/);
  return match ? match[1] : null;
}

function extractTitle(html) {
  const match = html.match(/<title>([^<]*)<\/title>/);
  return match ? match[1] : null;
}

function extractCanonicalUrl(html) {
  const match = html.match(/<link rel="canonical" href="([^"]*)"[^>]*>/);
  return match ? match[1] : null;
}

function validateMetaDescription(description, pagePath) {
  const issues = [];
  
  if (!description) {
    issues.push('Missing meta description');
    return { isValid: false, issues, length: 0 };
  }
  
  const length = description.length;
  
  if (length < SEO_CONSTANTS.META_DESCRIPTION_MIN_LENGTH) {
    issues.push(`Too short (${length} chars). Recommended: ${SEO_CONSTANTS.META_DESCRIPTION_MIN_LENGTH}-${SEO_CONSTANTS.META_DESCRIPTION_MAX_LENGTH} chars`);
  } else if (length > SEO_CONSTANTS.META_DESCRIPTION_MAX_LENGTH) {
    issues.push(`Too long (${length} chars). Recommended: ${SEO_CONSTANTS.META_DESCRIPTION_MIN_LENGTH}-${SEO_CONSTANTS.META_DESCRIPTION_MAX_LENGTH} chars`);
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    length
  };
}

function findHtmlFiles(dir, files = []) {
  const entries = fs.readdirSync(dir);
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findHtmlFiles(fullPath, files);
    } else if (entry === 'index.html') {
      files.push(fullPath);
    }
  }
  
  return files;
}

function getPagePath(filePath) {
  const relativePath = path.relative(DIST_DIR, filePath);
  if (relativePath === 'index.html') return '/';
  return '/' + relativePath.replace(/\/index\.html$/, '/').replace(/\\/g, '/');
}

function main() {
  console.log('🔍 Validating SEO implementation...\n');
  
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ Dist directory not found. Please run "npm run build" first.');
    process.exit(1);
  }
  
  const htmlFiles = findHtmlFiles(DIST_DIR);
  let totalIssues = 0;
  const results = [];
  
  for (const filePath of htmlFiles) {
    const html = fs.readFileSync(filePath, 'utf-8');
    const pagePath = getPagePath(filePath);
    
    const title = extractTitle(html);
    const description = extractMetaDescription(html);
    const canonicalUrl = extractCanonicalUrl(html);
    
    const validation = validateMetaDescription(description, pagePath);
    
    const result = {
      pagePath,
      title,
      description,
      canonicalUrl,
      validation
    };
    
    results.push(result);
    
    // Print results
    console.log(`📄 ${pagePath}`);
    console.log(`   Title: ${title || '❌ Missing'}`);
    console.log(`   Description: ${description ? `✅ (${validation.length} chars)` : '❌ Missing'}`);
    console.log(`   Canonical: ${canonicalUrl || '❌ Missing'}`);
    
    if (validation.issues.length > 0) {
      console.log(`   Issues:`);
      validation.issues.forEach(issue => {
        console.log(`     ⚠️  ${issue}`);
      });
      totalIssues += validation.issues.length;
    }
    
    console.log('');
  }
  
  // Summary
  console.log('📊 Summary:');
  console.log(`   Pages analyzed: ${results.length}`);
  console.log(`   Total issues: ${totalIssues}`);
  
  if (totalIssues === 0) {
    console.log('✅ All pages have valid SEO meta descriptions!');
  } else {
    console.log('⚠️  Some pages have SEO issues that should be addressed.');
  }
  
  // Show description lengths
  console.log('\n📏 Meta Description Lengths:');
  results.forEach(result => {
    if (result.description) {
      const status = result.validation.isValid ? '✅' : '⚠️';
      console.log(`   ${status} ${result.pagePath}: ${result.validation.length} chars`);
    }
  });
}

main();
