---
import BaseLayout from '@layouts/BaseLayout.astro';
import { DEFAULT_DESCRIPTIONS } from '@utils/seo';
import '@styles/global.css';

const allPosts = Object.values(import.meta.glob('./posts/*.md', { eager: true })).sort(
  (a: any, b: any) => {
    return new Date(b.frontmatter.date).getTime() - new Date(a.frontmatter.date).getTime();
  },
);

const pageTitle = "Articles";
const metaDescription = DEFAULT_DESCRIPTIONS.blog;
---

<BaseLayout
  pageTitle={pageTitle}
  metaDescription={metaDescription}
  showBackLink={true}
  backLinkTitle="Index"
  backLinkUrl="/"
>
  <h1>Articles</h1>
  <ul class="blog-posts">
    {
      allPosts.map((post: any) => (
        <li>
          <a href={post.url}>
            <span>{post.frontmatter.title}</span>
            <time>
              {post.frontmatter.date.toString('yyyy-MM-dd').slice(0, 10).replaceAll('-', '.')}
            </time>
          </a>
        </li>
      ))
    }
  </ul>
</BaseLayout>

<style>
  ul {
    padding: 0;
    margin: 0;
  }

  ul:hover li {
    opacity: 0.25;
  }

  ul li:hover {
    opacity: 1;
  }

  ul li {
    transition: opacity 0.2s ease-out;
  }

  li {
    list-style: none;
    border-bottom: 1px solid var(--color-border);

    line-height: 28px;

    padding: 12px 0;
  }

  a {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 24px;

    text-decoration: none;
    text-wrap: pretty;
  }

  time {
    font-family: monospace;
    font-size: 0.875rem;
    letter-spacing: -0.05rem;

    color: var(--color-tertiary);
  }
</style>
