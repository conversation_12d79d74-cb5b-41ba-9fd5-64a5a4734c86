---
import Footer from '@components/Footer.astro';
import BackLink from '@components/BackLink.astro';

const { pageTitle, showBackLink = false, backLinkTitle, backLinkUrl } = Astro.props;
const isDev = import.meta.env.DEV;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content={Astro.generator} />
    <title>{pageTitle}</title>

    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    {
      !isDev && (
        <script
          defer
          id="fairlytics-id-ajcu6jd9k7ysd6"
          data-fairlyticskey="a1425ecfc336a87383e79488e075c38b"
          src="https://app.fairlytics.tech/tag/tag.js"
        />
      )
    }
  </head>

  <body>
    <div class="grid-container">
      <aside class="left-column">
        {showBackLink && <BackLink title={backLinkTitle} url={backLinkUrl} />}
      </aside>

      <main class="main-content">
        <slot />
      </main>

      <aside class="right-column">
        <!-- Right column remains empty by default -->
      </aside>
    </div>

    <Footer />
  </body>
</html>
