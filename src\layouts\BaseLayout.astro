---
import Footer from '@components/Footer.astro';
import BackLink from '@components/BackLink.astro';
import { DEFAULT_DESCRIPTIONS, SEO_CONSTANTS, truncateDescription } from '@utils/seo';

export interface Props {
  pageTitle: string;
  metaDescription?: string;
  showBackLink?: boolean;
  backLinkTitle?: string;
  backLinkUrl?: string;
  ogImage?: string;
  canonicalUrl?: string;
  noIndex?: boolean;
}

const {
  pageTitle,
  metaDescription,
  showBackLink = false,
  backLinkTitle,
  backLinkUrl,
  ogImage,
  canonicalUrl,
  noIndex = false
} = Astro.props;

const isDev = import.meta.env.DEV;

// Generate meta description with fallback
const finalMetaDescription = metaDescription
  ? truncateDescription(metaDescription)
  : DEFAULT_DESCRIPTIONS.homepage;

// Generate canonical URL
const finalCanonicalUrl = canonicalUrl || new URL(Astro.url.pathname, SEO_CONSTANTS.SITE_URL).href;

// Generate Open Graph image URL
const finalOgImage = ogImage || `${SEO_CONSTANTS.SITE_URL}/og-image.jpg`;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content={Astro.generator} />

    <!-- SEO Meta Tags -->
    <title>{pageTitle}</title>
    <meta name="description" content={finalMetaDescription} />
    <meta name="author" content={SEO_CONSTANTS.DEFAULT_AUTHOR} />
    <link rel="canonical" href={finalCanonicalUrl} />
    {noIndex && <meta name="robots" content="noindex, nofollow" />}

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content={pageTitle} />
    <meta property="og:description" content={finalMetaDescription} />
    <meta property="og:url" content={finalCanonicalUrl} />
    <meta property="og:site_name" content={SEO_CONSTANTS.SITE_NAME} />
    <meta property="og:locale" content={SEO_CONSTANTS.DEFAULT_LOCALE} />
    <meta property="og:image" content={finalOgImage} />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={pageTitle} />
    <meta name="twitter:description" content={finalMetaDescription} />
    <meta name="twitter:image" content={finalOgImage} />

    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />

    {
      !isDev && (
        <script
          is:inline
          defer
          id="fairlytics-id-ajcu6jd9k7ysd6"
          data-fairlyticskey="a1425ecfc336a87383e79488e075c38b"
          src="https://app.fairlytics.tech/tag/tag.js"
        />
      )
    }
  </head>

  <body>
    <div class="grid-container">
      <aside class="left-column">
        {showBackLink && <BackLink title={backLinkTitle} url={backLinkUrl} />}
      </aside>

      <main class="main-content">
        <slot />
      </main>

      <aside class="right-column">
        <!-- Right column remains empty by default -->
      </aside>
    </div>

    <Footer />
  </body>
</html>
