---
import BaseLayout from '@layouts/BaseLayout.astro';
import '@styles/global.css';

const { frontmatter } = Astro.props;

const postDate = new Date(frontmatter.date)
  ?.toLocaleDateString('fr', { dateStyle: 'long' })
  ?.replace(/./, (c) => c.toUpperCase());
---

<BaseLayout
  pageTitle={frontmatter.title}
  showBackLink={true}
  backLinkTitle="Articles"
  backLinkUrl="/posts"
>
  <div class="header">
    <h1>{frontmatter.title}</h1>
    <time>le {postDate}</time>
  </div>

  <article>
    <slot />
  </article>
</BaseLayout>

<style>
  h1 {
    margin: 0;
    font-weight: 500;
  }

  time {
    font-family: var(--font-secondary);
    color: var(--color-tertiary);
  }

  .header {
    margin-bottom: 42px;
  }
</style>

<style is:global>
  article h2 {
    margin: 56px 0 28px;
  }
</style>
